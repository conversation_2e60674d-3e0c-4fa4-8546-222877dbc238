package auth

import (
	"context"
	"errors"
	"exert-app-service/internal/util"
	"log"
	"net/http"
	"time"

	"github.com/danielgtaylor/huma/v2"
)

type AuthHandler struct {
	authService *AuthService
}

func NewAuthHandler(authService *AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Huma Request/Response Types

// RegisterInput represents the request payload for user registration
type RegisterInput struct {
	Body struct {
		Username string `json:"username" minLength:"3" maxLength:"50" example:"john_doe" doc:"Username for the account"`
		Email    string `json:"email" format:"email" example:"<EMAIL>" doc:"Email address"`
		Password string `json:"password" minLength:"8" example:"password123" doc:"Password (minimum 8 characters)"`
	}
}

// RegisterOutput represents the response for user registration
type RegisterOutput struct {
	Body struct {
		ID       int32  `json:"id" example:"1" doc:"User ID"`
		Username string `json:"username" example:"john_doe" doc:"Username"`
		Email    string `json:"email" example:"<EMAIL>" doc:"Email address"`
	}
}

// LoginInput represents the request payload for user login
type LoginInput struct {
	Body struct {
		Username string `json:"username" minLength:"3" example:"john_doe" doc:"Username"`
		Password string `json:"password" minLength:"1" example:"password123" doc:"Password"`
	}
}

// LoginOutput represents the response for user login
type LoginOutput struct {
	Body struct {
		AccessToken  string    `json:"access_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." doc:"JWT access token"`
		RefreshToken string    `json:"refresh_token" example:"550e8400-e29b-41d4-a716-446655440000" doc:"Refresh token"`
		ExpiresAt    time.Time `json:"expires_at" example:"2024-01-01T12:00:00Z" doc:"Token expiration time"`
	}
}

// RefreshInput represents the request payload for token refresh
type RefreshInput struct {
	Body struct {
		RefreshToken string `json:"refresh_token" minLength:"1" example:"550e8400-e29b-41d4-a716-446655440000" doc:"Refresh token"`
	}
}

// RefreshOutput represents the response for token refresh
type RefreshOutput struct {
	Body struct {
		AccessToken  string    `json:"access_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." doc:"New JWT access token"`
		RefreshToken string    `json:"refresh_token" example:"550e8400-e29b-41d4-a716-446655440000" doc:"New refresh token"`
		ExpiresAt    time.Time `json:"expires_at" example:"2024-01-01T12:00:00Z" doc:"Token expiration time"`
	}
}

// LogoutInput represents the request payload for logout
type LogoutInput struct {
	Body struct {
		RefreshToken string `json:"refresh_token" minLength:"1" example:"550e8400-e29b-41d4-a716-446655440000" doc:"Refresh token to revoke"`
	}
}

// LogoutOutput represents the response for logout
type LogoutOutput struct {
	Body struct {
		Message string `json:"message" example:"Successfully logged out" doc:"Logout confirmation message"`
	}
}

// MeOutput represents the response for getting current user info
type MeOutput struct {
	Body struct {
		ID        int32     `json:"id" example:"1" doc:"User ID"`
		Username  string    `json:"username" example:"john_doe" doc:"Username"`
		Email     string    `json:"email" example:"<EMAIL>" doc:"Email address"`
		CreatedAt time.Time `json:"created_at" example:"2024-01-01T12:00:00Z" doc:"Account creation time"`
	}
}

// Legacy types for backward compatibility during migration
type RegisterRequest struct {
	Username string `json:"username"`
	Email    string `json:"email"`
	Password string `json:"password"`
}

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type TokenResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
}

type RefreshRequest struct {
	RefreshToken string `json:"refresh_token"`
}

// Register handles user registration
func (h *AuthHandler) Register(w http.ResponseWriter, r *http.Request) {
	var req RegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		util.RespondWithError(w, http.StatusBadRequest, "Invalid request payload")
		return
	}

	// Validate request
	if req.Username == "" || req.Email == "" || req.Password == "" {
		util.RespondWithError(w, http.StatusBadRequest, "Username, email, and password are required")
		return
	}

	// Register user
	user, err := h.authService.RegisterUser(r.Context(), req.Username, req.Email, req.Password)
	if err != nil {
		log.Println("error registering user: ", err)
		util.RespondWithError(w, http.StatusInternalServerError, "Failed to register user")
		return
	}

	util.RespondWithJSON(w, http.StatusCreated, map[string]interface{}{
		"id":       user.ID,
		"username": user.Username,
		"email":    user.Email,
	})
}

// Login handles user login
func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
	var req LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		util.RespondWithError(w, http.StatusBadRequest, "Invalid request payload")
		return
	}

	// Validate request
	if req.Username == "" || req.Password == "" {
		util.RespondWithError(w, http.StatusBadRequest, "Username and password are required")
		return
	}

	// Authenticate user
	expiresAt, accessToken, refreshToken, err := h.authService.AuthenticateUser(r.Context(), req.Username, req.Password)
	if err != nil {
		log.Println("error authenticating user: ", err)
		if err == ErrInvalidCredentials {
			util.RespondWithError(w, http.StatusUnauthorized, "Invalid credentials")
			return
		}
		util.RespondWithError(w, http.StatusInternalServerError, "Failed to authenticate user")
		return
	}

	util.RespondWithJSON(w, http.StatusOK, TokenResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
	})
}

// Refresh handles token refresh
func (h *AuthHandler) Refresh(w http.ResponseWriter, r *http.Request) {
	var req RefreshRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		util.RespondWithError(w, http.StatusBadRequest, "Invalid request payload")
		return
	}

	// Validate request
	if req.RefreshToken == "" {
		util.RespondWithError(w, http.StatusBadRequest, "Refresh token is required")
		return
	}

	// Refresh tokens
	expiresAt, accessToken, refreshToken, err := h.authService.RefreshTokens(r.Context(), req.RefreshToken)
	if err != nil {
		log.Println("error refreshing tokens: ", err)
		switch {
		case errors.Is(err, ErrInvalidToken):
			util.RespondWithError(w, http.StatusUnauthorized, "Invalid refresh token")
		case errors.Is(err, ErrTokenExpired):
			util.RespondWithError(w, http.StatusUnauthorized, "Refresh token expired")
		default:
			util.RespondWithError(w, http.StatusInternalServerError, "Failed to refresh tokens")
		}
		return
	}

	util.RespondWithJSON(w, http.StatusOK, TokenResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
	})
}

// Logout handles user logout
func (h *AuthHandler) Logout(w http.ResponseWriter, r *http.Request) {
	var req RefreshRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		util.RespondWithError(w, http.StatusBadRequest, "Invalid request payload")
		return
	}

	// Validate request
	if req.RefreshToken == "" {
		util.RespondWithError(w, http.StatusBadRequest, "Refresh token is required")
		return
	}

	// Revoke token
	if err := h.authService.RevokeToken(r.Context(), req.RefreshToken); err != nil {
		util.RespondWithError(w, http.StatusInternalServerError, "Failed to logout")
		return
	}

	util.RespondWithJSON(w, http.StatusOK, map[string]string{"message": "Successfully logged out"})
}

// Me returns the current user
func (h *AuthHandler) Me(w http.ResponseWriter, r *http.Request) {
	userID, ok := GetUserID(r)
	if !ok {
		util.RespondWithError(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	user, err := h.authService.GetUserByID(r.Context(), userID)
	if err != nil {
		log.Println("error getting user by id: ", err)
		if err == ErrUserNotFound {
			util.RespondWithError(w, http.StatusNotFound, "User not found")
			return
		}
		util.RespondWithError(w, http.StatusInternalServerError, "Failed to get user")
		return
	}

	util.RespondWithJSON(w, http.StatusOK, map[string]interface{}{
		"id":         user.ID,
		"username":   user.Username,
		"email":      user.Email,
		"created_at": user.CreatedAt,
	})
}
