package program

import (
	"exert-app-service/internal/db"
	"time"
)

// Huma Request/Response Types for Program Operations

// CreateProgramInput represents the request payload for creating a program
type CreateProgramInput struct {
	Body struct {
		Name            string                     `json:"name" minLength:"1" maxLength:"100" example:"Push Pull Legs" doc:"Program name"`
		Description     string                     `json:"description" maxLength:"500" example:"A 3-day split program" doc:"Program description"`
		ProgramType     db.ProgramType             `json:"programType" enum:"STRENGTH,HYPERTROPHY,ENDURANCE,POWER" example:"STRENGTH" doc:"Type of program"`
		ProgramRoutines []CreateProgramRoutineData `json:"routines" doc:"List of routines in the program"`
	}
}

// CreateProgramRoutineData represents routine data for program creation
type CreateProgramRoutineData struct {
	Name        string                  `json:"name" minLength:"1" maxLength:"100" example:"Push Day" doc:"Routine name"`
	Description string                  `json:"description" maxLength:"500" example:"Chest, shoulders, triceps" doc:"Routine description"`
	Order       int32                   `json:"order" minimum:"1" example:"1" doc:"Order of routine in program"`
	Groups      []CreateProgramGroupData `json:"groups" doc:"List of exercise groups in routine"`
}

// CreateProgramGroupData represents group data for program creation
type CreateProgramGroupData struct {
	Name  string `json:"name" minLength:"1" maxLength:"100" example:"Bench Press" doc:"Exercise name"`
	Sets  int32  `json:"sets" minimum:"1" maximum:"20" example:"3" doc:"Number of sets"`
	Reps  int32  `json:"reps" minimum:"1" maximum:"100" example:"10" doc:"Number of reps"`
	Order int32  `json:"order" minimum:"1" example:"1" doc:"Order of exercise in routine"`
}

// UpdateProgramInput represents the request payload for updating a program
type UpdateProgramInput struct {
	Body struct {
		ID              int32                      `json:"id" minimum:"1" example:"1" doc:"Program ID"`
		Name            string                     `json:"name" minLength:"1" maxLength:"100" example:"Push Pull Legs" doc:"Program name"`
		Description     string                     `json:"description" maxLength:"500" example:"A 3-day split program" doc:"Program description"`
		ProgramType     db.ProgramType             `json:"programType" enum:"STRENGTH,HYPERTROPHY,ENDURANCE,POWER" example:"STRENGTH" doc:"Type of program"`
		ProgramRoutines []UpdateProgramRoutineData `json:"routines" doc:"List of routines in the program"`
	}
}

// UpdateProgramRoutineData represents routine data for program update
type UpdateProgramRoutineData struct {
	ID          int32                   `json:"id" example:"1" doc:"Routine ID (0 for new routines)"`
	Name        string                  `json:"name" minLength:"1" maxLength:"100" example:"Push Day" doc:"Routine name"`
	Description string                  `json:"description" maxLength:"500" example:"Chest, shoulders, triceps" doc:"Routine description"`
	Order       int32                   `json:"order" minimum:"1" example:"1" doc:"Order of routine in program"`
	Groups      []UpdateProgramGroupData `json:"groups" doc:"List of exercise groups in routine"`
	ProgramID   int32                   `json:"programId" example:"1" doc:"Program ID this routine belongs to"`
}

// UpdateProgramGroupData represents group data for program update
type UpdateProgramGroupData struct {
	ID        int32  `json:"id" example:"1" doc:"Group ID (0 for new groups)"`
	Name      string `json:"name" minLength:"1" maxLength:"100" example:"Bench Press" doc:"Exercise name"`
	Sets      int32  `json:"sets" minimum:"1" maximum:"20" example:"3" doc:"Number of sets"`
	Reps      int32  `json:"reps" minimum:"1" maximum:"100" example:"10" doc:"Number of reps"`
	Order     int32  `json:"order" minimum:"1" example:"1" doc:"Order of exercise in routine"`
	RoutineID int32  `json:"routineId" example:"1" doc:"Routine ID this group belongs to"`
}

// ProgramOutput represents the response for program operations
type ProgramOutput struct {
	Body WorkoutProgramDao `json:",inline"`
}

// ProgramsOutput represents the response for listing programs
type ProgramsOutput struct {
	Body struct {
		Programs []WorkoutProgramDao `json:"programs" doc:"List of workout programs"`
	}
}

// GetProgramInput represents the request for getting a specific program
type GetProgramInput struct {
	ProgramID int32 `path:"programId" minimum:"1" example:"1" doc:"Program ID"`
}

// DeleteProgramInput represents the request for deleting a program
type DeleteProgramInput struct {
	ProgramID int32 `path:"programId" minimum:"1" example:"1" doc:"Program ID"`
}

// DeleteProgramOutput represents the response for deleting a program
type DeleteProgramOutput struct {
	Body struct {
		Message string `json:"message" example:"Program deleted successfully" doc:"Deletion confirmation message"`
	}
}

// ProgramResponseData represents the detailed program data in responses
type ProgramResponseData struct {
	ID              int32                       `json:"id" example:"1" doc:"Program ID"`
	Name            string                      `json:"name" example:"Push Pull Legs" doc:"Program name"`
	Description     string                      `json:"description" example:"A 3-day split program" doc:"Program description"`
	ProgramType     db.ProgramType              `json:"programType" example:"STRENGTH" doc:"Type of program"`
	ProgramRoutines []ProgramRoutineResponseData `json:"routines" doc:"List of routines in the program"`
}

// ProgramRoutineResponseData represents routine data in responses
type ProgramRoutineResponseData struct {
	ID          int32                      `json:"id" example:"1" doc:"Routine ID"`
	Name        string                     `json:"name" example:"Push Day" doc:"Routine name"`
	Description string                     `json:"description" example:"Chest, shoulders, triceps" doc:"Routine description"`
	Order       int32                      `json:"order" example:"1" doc:"Order of routine in program"`
	Groups      []ProgramGroupResponseData `json:"groups" doc:"List of exercise groups in routine"`
	ProgramID   int32                      `json:"programId" example:"1" doc:"Program ID this routine belongs to"`
}

// ProgramGroupResponseData represents group data in responses
type ProgramGroupResponseData struct {
	ID        int32  `json:"id" example:"1" doc:"Group ID"`
	Name      string `json:"name" example:"Bench Press" doc:"Exercise name"`
	Sets      int32  `json:"sets" example:"3" doc:"Number of sets"`
	Reps      int32  `json:"reps" example:"10" doc:"Number of reps"`
	Order     int32  `json:"order" example:"1" doc:"Order of exercise in routine"`
	RoutineID int32  `json:"routineId" example:"1" doc:"Routine ID this group belongs to"`
}
